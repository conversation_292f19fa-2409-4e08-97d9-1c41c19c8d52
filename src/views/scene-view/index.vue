<template>
  <ScaleBox>
    <div class="datav-box">
      <div class="data-heard">
        <div class="heard-center">智能云监控大屏</div>
        <div class="left-title">
          <!-- <div>
            <div>数据最后</div>
            <div>更新时间</div>
          </div> -->
          <div class="left-time-text">{{ nowDate }} {{ this.getWeekDate() }} {{ hourTimes }}</div>
        </div>
        <div class="left-time left-r">
          <div>
            <div>下次更新</div>
            <div>剩余时间</div>
          </div>
          <count-down
            style="margin-left: 8px"
            :timestamp="timestamp"
            v-show="timeOver"
            :autoplay="false"
            ref="uCountDown"
            @end="isEnd"
          ></count-down>
        </div>
        <!-- <div class="right-time">
          <div class="rght-l">
            当前时间 : <span class="right-time-text">{{ nowDate }}</span>
          </div>
          <div class="div-r"></div>
        </div>
        <div class="right-hour">{{ hourTime }}</div> -->
      </div>
      <div class="content-box">
        <div class="data-left">
          <div class="left-top">
            <dv-border-box-11
              :title="`${city}告警基站统计`"
              class="left-statistics"
            >
              <dv-loading v-show="loading"></dv-loading>
              <div class="alarm-box" v-show="!loading">
                <div class="alarm-top">
                  <div class="alarm-content" @click="handleDetail('停电')">
                    <img src="~@/assets/images/icon_electric.png" />
                    <div>
                      <div class="font18 alarm-red">
                      {{ alarmData.powerOutage }}
                      </div>
                      <div class="font28">停电</div>
                    </div>
                  </div>

                  <div class="alarm-content" @click="handleDetail('欠压')">
                    <img src="~@/assets/images/icon_low_temperature.png" />
                    <div>
                      <div class="font18 alarm-red">
                      {{ alarmData.underVoltage }}
                      </div>
                      <div class="font28">欠压</div>
                    </div>
                  </div>

                  <div class="alarm-content" @click="handleDetail('退服')">
                    <img src="~@/assets/images/icon_withdrawal.png" />
                    <div>
                      <div class="font18 alarm-g">
                      {{ alarmData.quitServer }}
                      </div>
                      <div class="font28">退服</div>
                    </div>
                  </div>

                  <div class="alarm-content" @click="handleDetail('疑似退服')">
                    <img src="~@/assets/images/icon_withdrawal.png" />
                    <div>
                      <div class="font18 alarm-g">
                      {{ alarmData.likeOutServer }}
                      </div>
                      <div class="font28">疑似退服</div>
                    </div>
                  </div>
                </div>
                <div class="alarm-top">
                  <div class="alarm-content" @click="handleDetail('离线')">
                    <img src="~@/assets/images/icon_off-line.png" />
                    <div>
                      <div class="font18 alarm-l">
                      {{ alarmData.outLine }}
                      </div>
                      <div class="font28">离线</div>
                    </div>
                  </div>
                  <div class="alarm-content" @click="handleDetail('温度过高')">
                    <img src="~@/assets/images/icon_high_temperature.png" />
                    <div>
                      <div class="font18 alarm-red">
                      {{ alarmData.wdgg }}
                      </div>
                      <div class="font28">温度过高</div>
                    </div>
                  </div>
                  <div class="alarm-content" @click="handleDetail('烟雾/火灾')">
                    <img src="~@/assets/images/icon_smoke.png" />
                    <div>
                      <div class="font18 alarm-g">
                      {{ alarmData.fireSmoke }}
                      </div>
                      <div class="font28">烟雾/火灾</div>
                    </div>
                  </div>
                  <div class="alarm-content" @click="handleDetail('水浸')">
                    <img src="~@/assets/images/icon_water_immersion.png" />
                    <div>
                      <div class="font18 alarm-red">
                      {{ alarmData.waterImmersion }}
                      </div>
                      <div class="font28">水浸</div>
                    </div>
                  </div>
                </div>
              </div>
            </dv-border-box-11>
          </div>
          <div class="left-bottom" ref="tableH">
            <div class="top-title">
              {{ city }}{{ city === "全省" ? "各地市" : "各区县" }}告警基站统计
            </div>
            <dv-border-box-1 style="width: 100%; height: 100%;">
              <dv-loading v-show="tableLoading"></dv-loading>
              <div class="table-box">
                <el-table
                  :data="alarmStatistics"
                  style="width: 100%"
                  :height="tableH + 'px'"
                  :row-class-name="tableRowClassName"
                >
                  <el-table-column
                    :prop="city == '全省' ? 'city' : 'county'"
                    :label="city == '全省' ? '地市' : '区县'"
                    align="center"
                    min-width="50"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="powerOutage"
                    label="停电"
                    align="center"
                    min-width="50"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="underVoltage"
                    label="欠压"
                    min-width="50"
                    align="center"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="quitServer"
                    label="退服"
                    align="center"
                    min-width="50"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="likeOutServer"
                    label="疑似退服"
                    min-width="60"
                    align="center"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="outLine"
                    label="离线"
                    align="center"
                    min-width="50"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="wdgg"
                    label="温度过高"
                    align="center"
                    min-width="50"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="fireSmoke"
                    label="烟雾/火灾"
                    min-width="50"
                    align="center"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="waterImmersion"
                    label="水浸"
                    min-width="50"
                    align="center"
                  >
                  </el-table-column>
                </el-table>
              </div>
            </dv-border-box-1>
          </div>
        </div>
        <div class="data-centent">
          <div class="map-time-box">
            <div class="map-time-lable">设置轮播时间：</div>
            <div class="map-time-value">
              <el-input style="width: 84px" v-model="timeChange">
                <template #suffix>秒</template>
              </el-input>
              <img src="~@/assets/images/icon_time.png" class="map-icon-time" />
            </div>
            <div class="map-time-btn" @click="handleOk">确定</div>
          </div>
          <div class="type-box">
            <div class="type-item" :class="{ active: type === '铁塔' }" @click="handleType('铁塔')">铁塔</div>
            <div class="type-item" :class="{ active: type === '移动' }" @click="handleType('移动')">移动</div>
            <div class="type-item" :class="{ active: type === '联通' }" @click="handleType('联通')">联通</div>
            <div class="type-item" :class="{ active: type === '电信' }" @click="handleType('电信')">电信</div>
          </div>
          <div class="map-title">{{ text }}</div>
          <map-chart
            :chartData="alarmStatistics"
            :visualMapPieces="visualMapPieces"
            @change="getText"
            @setCity="getCity"
            @on-rule-click="handleRuleClick"
            ref="mapChart"
          ></map-chart>
        </div>
        <div class="data-right">
          <div class="right-top">
            <dv-border-box-11 title="气象预警" style="height: 100%">
              <dv-loading v-show="weatherLoading"></dv-loading>
              <div
                v-show="!fsuShow"
                class="alert-contnet"
                style="width: 100%; height: 100%"
              >
                <div class="back-btn">
                  <el-button
                    type="text"
                    style="color: #04c5f3"
                    icon="el-icon-refresh-right"
                    @click="handleBack"
                  >
                    返回
                  </el-button>
                </div>
                <div class="alert-detail">
                  <div
                    v-for="(v, i) in fsuList"
                    :key="v.id"
                    class="alert-box"
                    :class="i % 2 === 0 ? 'alert-bg' : ''"
                  >
                    <img :src="v.warningPicture" />
                    <div>
                      <div class="alert-title">
                        <div class="early-warning-item-label">
                          <span>{{ v.happenTime }}</span
                          ><span style="margin-left: 5px">
                            {{
                              `${v.province ? v.province : ""}${
                                v.city ? v.city : ""
                              }${v.county ? v.county : ""}发布${v.warningType}`
                            }}</span
                          >
                        </div>
                      </div>
                      <div>
                        <span class="item-detail-label">
                          预警详情：{{ v.warningContent }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="powerCut" v-show="!weatherLoading && fsuShow">
                <el-row :gutter="16" style="padding-top: 46px; width: 94%">
                  <el-col
                    :md="{ span: '8' }"
                    v-for="(item, index) in weatherData"
                    :key="index"
                  >
                    <div class="gutter-box">
                      <div class="city-box" @click="toDetail(item.date)">
                        <div class="alert-icon" v-if="item.weight === 1"></div>
                        <div
                          class="alert-icon-normal"
                          v-if="item.weight === 0 || item.weight === null"
                        ></div>
                        <div
                          class="alarm-orange"
                          v-if="item.weight === 2"
                        ></div>
                        <div
                          class="alarm-yellow"
                          v-if="item.weight === 3"
                        ></div>
                        <div class="alarm-blue" v-if="item.weight === 4"></div>
                        <div>{{ item.city }}</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </dv-border-box-11>
          </div>
          <div class="right-bottom">
            <div class="top-title">
              <p>
                {{ rightBottomTitle }}
              </p>
              <div class="report-btn" @click="handleReport">简报信息</div>
            </div>
            <dv-border-box-1 style="height: 100%">
              <dv-loading v-show="orderLoading"></dv-loading>
              <div v-show="rightBottomTitle === '故障工单超时预警'">
                <div class="table-box">
                  <el-table
                    :data="timeoutList"
                    style="width: 100%"
                    :height="tableH - '30' + 'px'"
                    align-header="center"
                    :row-class-name="tableRowClassName"
                    @row-click="handleOvertimeTable(row)"
                  >
                    <el-table-column
                      :prop="city == '全省' ? 'city' : 'county'"
                      :label="city == '全省' ? '地市' : '区县'"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="total"
                      label="故障总数"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="over3Hour"
                      label="超3小时"
                      min-width="60"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="over6Hour"
                      label="超6小时"
                      width="60"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="over8Hour"
                      label="超8小时"
                      min-width="50"
                      align="center"
                    >
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div v-show="rightBottomTitle !== '故障工单超时预警'">
                <div class="time-selector-box">
                  <div class="time-selector-label">时间配置：</div>
                  <div class="time-picker-wrapper">
                    <el-date-picker
                      v-model="listStartTime"
                      type="date"
                      placeholder="请选择开始时间"
                      value-format="YYYY-MM-DD"
                      :picker-options="startTimePickerOptions"
                      @change="handleTimeChange"
                      class="time-picker-input"
                    ></el-date-picker>
                    <!-- <img src="~@/assets/images/icon_time.png" class="time-picker-icon" /> -->
                  </div>
                  <span class="time-separator">至</span>
                  <div class="time-picker-wrapper">
                    <el-date-picker
                      v-model="listEndTime"
                      type="date"
                      placeholder="请选择结束时间"
                      value-format="YYYY-MM-DD"
                      :picker-options="endTimePickerOptions"
                      @change="handleTimeChange"
                      class="time-picker-input"
                    ></el-date-picker>
                    <!-- <img src="~@/assets/images/icon_time.png" class="time-picker-icon" /> -->
                  </div>
                </div>
                <div class="table-box" style="padding-top: 0;">
                  <el-table
                    :data="top50List"
                    style="width: 100%"
                    :height="tableH - '30' + 'px'"
                    align-header="center"
                    :row-class-name="tableRowClassName"
                    @row-click="handleTop50Table(row)"
                  >
                    <el-table-column
                      prop="rank"
                      label="排名"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      :prop="city == '全省' ? 'city' : 'county'"
                      :label="city == '全省' ? '地市' : '区县'"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="siteName"
                      label="站址名称"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="siteCode"
                      label="站址编码"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="count"
                      label="告警数"
                      align="center"
                      min-width="50"
                    >
                    </el-table-column>
                  </el-table>
                </div>
              </div>
              <div class="switch-box">
                <div
                  class="switch-style"
                  :class="{ bg :rightBottomTitle === '故障工单超时预警' }"
                  @mouseenter="handleMouseenter(index)"
                >
                </div>
                <div
                  class="switch-style"
                  :class="{ bg :rightBottomTitle !== '故障工单超时预警' }"
                  @mouseenter="handleMouseenter(index)"
                >
                </div>
              </div>
            </dv-border-box-1>
          </div>
        </div>
      </div>
      <modal-table ref="modalTable"></modal-table>
      <offLine-table ref="offLineTable"></offLine-table>
      <powerOn-table ref="powerOnTable"></powerOn-table>
      <overtime-table ref="overtimeTable"></overtime-table>
      <top50-table ref="top50Table"></top50-table>
      <rule-dialog ref="ruleDialog" @on-update="handleUpdateRule"></rule-dialog>
    </div>
  </ScaleBox>
</template>

<script>
// import countDown from "@/components/base/countDown";
import ScaleBox from "@/components/base/ScaleBox";

import mapChart from "./modal/mapChart";
import modalTable from "./modal/modalTable";
import offLineTable from "./modal/offLineTable";
import powerOnTable from "./modal/powerOnTable";
import overtimeTable from "./modal/overtimeTable";
import top50Table from "./modal/top50Table";
import { Colors } from "./modal/mapChart.vue";
import ruleDialog from "./modal/rule-dialog";

import {
  getProvinceAlarmStatistics,
  getFaultTimeoutStatisticsPage,
  getWeatherWarningList,
  getStationAlarmList,
  getCityAlarmStatistics,
  getCountyAlarmStatistics,
  getProvinceCityAlarmStatistics,
  getAlarmPropDetail
} from "@/api/sceneView/index";

const BigScreenCountdown = () => new Promise().resolve({ code : 200, result: {}, data: {} })

export const CigType = [
  { label: '次数', value: '', flag: '' },
  { label: '比例', value: 'Percentage', flag: '%' }
]

export default {
  created() {
    this.nowTime = setInterval(() => {
      this.formatDate();
    }, 1000);
    // this.getCountdown();
    this.getRuleConfig();
  },
  data: function () {
    return {
      type: '铁塔',
      alarmStatistics: [],
      list: [],
      isMounted: false,
      alarmData: {
        powerOutage: 0, // 停电数量
        underVoltage: 0, // 欠压数量
        quitServer: 0, // 退服数量
        likeOutServer: 0, // 疑似退服数量
        outLine: 0, // 离线数量
        wdgg: 0, // 温度过高数量
        fireSmoke: 0, // 烟雾/火灾数量
        waterImmersion: 0, // 水浸数量
      },
      weatherData: [],
      text: "当前全省停电情况",
      city: "全省",
      loading: false,
      tableLoading: false,
      weatherLoading: false,
      fsuShow: true,
      fsuList: [],
      interval: null,
      orderLoading: false,
      timeOver: true,
      timestamp: 60,
      updateTime: "",
      nowDate: null,
      nowTime: null,
      hourTime: null,
      hourTimes: null,
      rightBottomTitle: '故障工单超时预警', // 故障工单超时预警 单站告警TOP50
      timeChange: localStorage.getItem("changeTime")
        ? localStorage.getItem("changeTime")
        : 10,
      // timeChange: 10,
      workOrderSum: 0,
      timeoutList: [],
      fsuAllList: [],
      top50List: [],
      ruleConfig: [],
      visualMapPieces: [],
      listStartTime: '', // 开始时间
      listEndTime: '', // 结束时间
      startTimePickerOptions: {
        disabledDate: (time) => {
          if (this.listEndTime) {
            return time.getTime() > new Date(this.listEndTime).getTime();
          }
          return false;
        }
      },
      endTimePickerOptions: {
        disabledDate: (time) => {
          if (this.listStartTime) {
            return time.getTime() < new Date(this.listStartTime).getTime();
          }
          return false;
        }
      }
    };
  },
  mounted() {
    this.isMounted = true;
    this.getAlarmData();
    this.getAlarmCityData();
    // this.getWorkOrderData(); empty function
    this.getWeatherData();
    this.getFaultTimeoutStatisticsPage();
    this.getTop50Data();
    if (process.env.NODE_ENV === "development") {
      console.clear();
    }
  },
  methods: {
    preZero (v = 0) {
      return v >= 10 ? v : `0${v}`;
    },
    async getFaultTimeoutStatisticsPage() {
      this.timeoutList = [];

      // 计算时间范围
      // const currentDate = new Date();
      // const endTime = new Date(currentDate.getTime() - 24 * 60 * 60 * 1000); // 昨天
      // const startTime = new Date(endTime.getTime() - 6 * 24 * 60 * 60 * 1000); // 7天前

      // // 格式化时间为 YYYY-MM-DD HH:mm:ss
      // const formatDateTime = (date) => {
      //   const year = date.getFullYear();
      //   const month = this.preZero(date.getMonth() + 1);
      //   const day = this.preZero(date.getDate());
      //   return `${year}-${month}-${day} 00:00:00`;
      // };

      // const startTimeStr = formatDateTime(startTime);
      // const endTimeStr = formatDateTime(endTime);

      // 获取全省数据
      let { result, code } = await getFaultTimeoutStatisticsPage({
        city: this.city === "全省" ? "" : this.city,
        // startTime: startTimeStr,
        // endTime: endTimeStr,
        pageNum: 1,
        pageSize: 100
      });

      if (code === 200 && result && result.length > 0) {
        this.timeoutList = result;
      }
    },
    async getTop50Data() {
      let params = {
        city: this.city === "全省" ? "" : this.city,
      };

      if (this.listStartTime) {
        params.listStartTime = this.listStartTime;
      }
      if (this.listEndTime) {
        params.listEndTime = this.listEndTime;
      }

      let { result, code } = await getStationAlarmList(params);
      if (code === 200 && result && result.length > 0) {
        this.top50List = result;
      }
    },
    handleTimeChange() {
      if ((this.listStartTime && this.listEndTime) || (!this.listStartTime && !this.listEndTime)) {
        this.getTop50Data();
      }
    },
    handleOk() {
      localStorage.setItem("changeTime", this.timeChange);
      this.$refs.mapChart.getTime(this.timeChange);
      this.$message.success(`设置成功，当前轮播时间:${this.timeChange}秒`);
    },
    async getCountdown() {
      let { result, code } = await BigScreenCountdown();
      if (code == 200) {
        this.timeOver = true;
        this.timestamp = result.nextTime;
        this.updateTime = result.updateTime;
        this.getAlarmData();
        // this.getAlarmCityData();
        this.getWorkOrderData();
        this.getWeatherData();
        this.getFaultTimeoutStatisticsPage();
        this.$refs.uCountDown.start();
      }
    },
    isEnd() {
      this.timeOver = false;
      this.getCountdown();
    },
    formatDate() {
      let date = new Date();
      let year = date.getFullYear(); // 年
      let month = date.getMonth() + 1; // 月
      let day = date.getDate(); // 日
      let hour = date.getHours(); // 时
      hour = hour < 10 ? "0" + hour : hour; // 如果只有一位，则前面补零
      let minute = date.getMinutes(); // 分
      minute = minute < 10 ? "0" + minute : minute; // 如果只有一位，则前面补零
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      this.nowDate = `${year}.${month}.${day}`;
      this.hourTime = `${hour} : ${minute}`;
      this.hourTimes = `${hour} : ${minute} : ${s}`;
    },
    getWeekDate() {
      var now = new Date();
      var day = now.getDay();
      var weeks = new Array(
        "周日",
        "周一",
        "周二",
        "周三",
        "周四",
        "周五",
        "周六"
      );
      var week = weeks[day];
      return week;
    },
    toDetail(data) {
      this.fsuList = data;
      this.fsuShow = false;
    },
    handleBack() {
      this.fsuShow = true;
      this.city = '全省'
    },
    //获取地图头部信息
    getText(val) {
      this.text = val;
    },
    //获取地市
    getCity(val) {
      this.city = val ? val : "全省";
      this.fsuShow = true;
      this.getAlarmData();
      this.getAlarmCityData();
      this.getWeatherData();
      // this.getWorkOrderData();
      this.getFaultTimeoutStatisticsPage();
    },
    handleType(type) {
      this.type = type;
    },
    handleDetail(alarmType) {
      if (alarmType === "疑似退服") {
        this.$refs.powerOnTable.initForm({
          alarmType: alarmType,
          city: this.city === "全省" ? "" : this.city,
      })
      }
      else if (alarmType === "离线") {
        this.$refs.offLineTable.initForm(
          this.city === "全省" ? "" : this.city
        )
      }
      else {
        this.$refs.modalTable.initForm({
          alarmType: alarmType,
          city: this.city === "全省" ? "" : this.city,
        });
      }
    },
    handleOvertimeTable(row) {
      this.$refs.overtimeTable.initForm({
        // city: this.city === "全省" ? "" : this.city,
        // county: row.county
      });
    },
    handleTop50Table(row) {
      this.$refs.top50Table.initForm({
        // city: this.city === "全省" ? "" : this.city,
        // county: row.county
      });
    },
    //气象预警
    async getWeatherData() {
      this.weatherLoading = true;
      let { result, code } = await getWeatherWarningList();
      if (code == 200) {
        this.weatherData = result
          // .map(x => {
          //   x.date = x.date
          //     .map(y => {
          //       y.warningPicture =
          //         y.warningPicture
          //           .replace('http://www.weather.com.cn', process.env.VUE_APP_WEATHER_HOST)
          //       return y
          //     })
          //   return x
          // })
      }
      this.weatherLoading = false;
    },
    async getAlarmCityData() {
      this.tableLoading = true;
      let http = getCityAlarmStatistics;
      const params = { type: this.type };
      if (this.city !== "全省") {
        http = getCountyAlarmStatistics;
        params.city = this.city;
      }
      let result = await http(params);
      this.alarmStatistics = result || [];
      this.tableLoading = false;
    },
    async getAlarmData() {
      this.loading = true;

      let http =
        this.city === "全省"
          ? getProvinceAlarmStatistics
          : getProvinceCityAlarmStatistics;
      const params = { type: this.type };
      if (this.city !== "全省") {
        params.city = this.city;
      }
      let result = await http(params);
      this.alarmData = {
        ...this.alarmData,
        ...result,
      };
      this.loading = false;
    },


    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "clickRowStyl" : "";
    },
    formatRuleItem(configItem) {
      let { keyword1, keyword2, keyword3, keyword4, keyword5, keyword6, keyword7, keyword8, alarmType, cigType } = configItem
      const cigTypeItem = CigType.find(x => x.label === cigType)
      const alarmFieldValue = alarmType + cigTypeItem.label
      const cigTypeFlag = cigTypeItem.flag

      return [
        { gte: keyword8, label: `${alarmFieldValue}>=${keyword7}${cigTypeFlag}`, color: Colors[4].value },
        { gte: keyword6, lt: keyword7, label: `${keyword6}${cigTypeFlag}=<${alarmFieldValue}<${keyword7}${cigTypeFlag}`, color: Colors[3].value },
        { gte: keyword4, lt: keyword5, label: `${keyword4}${cigTypeFlag}=<${alarmFieldValue}<${keyword5}${cigTypeFlag}`, color: Colors[2].value },
        { gte: keyword2, lt: keyword3, label: `${keyword2}${cigTypeFlag}=<${alarmFieldValue}<${keyword3}${cigTypeFlag}`, color: Colors[1].value },
        { lt: keyword1, label: `${alarmFieldValue}<${keyword1}${cigTypeFlag}`, color: Colors[0].value },
      ]
    },
    setVisualMapPieces() {
      if (this.ruleConfig.length === 0) {
        return
      }
      this.visualMapPieces = this.ruleConfig.map(x => this.formatRuleItem(x)) || []
    },
    handleRuleClick({ alarmType }) {
      const configItem = this.ruleConfig.find(x => x.alarmType === alarmType)
      this.$refs.ruleDialog.initForm({
        alarmType,
        ...configItem,
      })
    },
    handleMouseenter () {
      this.rightBottomTitle =
        this.rightBottomTitle ===
          '故障工单超时预警' ? '单站告警TOP50' : '故障工单超时预警'
    },
    async getRuleConfig () {
      let result = await getAlarmPropDetail();
      this.ruleConfig = result.map(item => {
        Object.entries(item).map(([key, value]) => {
          if (key.startsWith('keyword')) {
            item[key] = +value
          }
          return item
        })
        // item.cigType = '次数'
        return item
      })

      this.setVisualMapPieces()
    },
    handleUpdateRule (newConfig) {
      const curItemIndex = this.ruleConfig.findIndex(item => {
        return item.alarmType === newConfig.alarmType
      })
      if (curItemIndex > -1) {
        this.$set(this.ruleConfig, curItemIndex, newConfig)
        this.setVisualMapPieces()
      }
    },
    handleReport () {
      this.$refs.reportTable.initForm()
    }
  },
  computed: {
    tableH() {
      if (this.isMounted) {
        return this.$refs.tableH.offsetHeight - 50;
      }
      return '';
    },
    // FIXME can't scroll.
  },
  watch: {},
  components: {
    // modalTable,
    // offLineTable,
    // powerOnTable,
    mapChart,
    // countDown,
    ruleDialog,
    overtimeTable,
    top50Table,
    ScaleBox,
  },
  beforeDestroy() {
    this.interval = null;
  },
};
</script>
<style scoped lang="scss">
.datav-box {
  margin: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-variant: tabular-nums;
  line-height: 1.5;
  font-feature-settings: "tnum", "tnum";
  width: 100%;
  height: 100%;
  background: url("@/assets/images/pic_bg.png");
  background-size: 100% 100%;
  overflow: hidden;
  &::-webkit-scrollbar {
    width: 1px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(144, 147, 153, 0.5);
  }
  &::-webkit-scrollbar-track {
    border-radius: 5px;
    background: transparent;
  }
}
.data-heard {
  background: url("@/assets/images/top_bg.png");
  background-size: 100% 100%;
  height: 56px;
  margin: 4px 10px 0 10px;
  position: relative;
  // display: flex;
  // justify-content: space-between;
  .heard-center {
    font-size: 32px;
    text-align: center;
    font-weight: bold;
    width: 100%;
    background: linear-gradient(180deg, #ffffff 0%, #00eaff 100%);
    color: #3fe1eb;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .left-r {
    left: 15.4%;
  }

  .left-time , .left-title {
    display: flex;
    position: absolute;
    align-items: center;
    color: #fff;
    top: 0;
    font-weight: 400;
    font-size: 13px;
    .left-time-text {
      padding-left: 18px;
      font-size: 18px;
      font-weight: bold;
    }
  }
  .left-title {
    top: 6px;
    .left-time-text{
      font-size: 20px;
    }
  }
  .right-time {
    display: flex;
    position: absolute;
    align-items: center;
    color: #fff;
    top: 0;
    right: 0;
    font-weight: 400;
    font-size: 14px;
    height: 100%;
    right: 7%;
    top: -4px;
    .rght-l {
      display: flex;
      align-items: center;
    }
    .right-time-text {
      font-size: 18px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
      margin: 0 27px 0 30px;
    }
    .div-r {
      width: 1px;
      height: 12px;
      background: #ffffff;
      opacity: 0.5;
    }
  }
  .right-hour {
    position: absolute;
    font-size: 24px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    top: 4px;
    right: 2%;
  }
}
.alarm-box {
  width: 100%;
  margin-top: 50px;
  // padding-left: 20px;
  .alarm-top {
    flex: 1;
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
    .alarm-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      flex: 1;
      text-align: center;
      // gap: 5px;
      cursor: pointer;
      img{
        width: 70px;
        height: 70px;
      }
      .alarm-title{
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }
    }
  }
}
.font18 {
  font-size: 30px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  line-height: 40px;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
}
.alarm-red {
  color: #ff0000;
  background: linear-gradient(0deg, #e30000 0%, #ff8585 100%);
}
.alarm-g {
  color: #ff7f00;
  background: linear-gradient(0deg, #ff7f00 0%, #fcc186 100%);
}
.alarm-l {
  color: #ffffff;
  background: linear-gradient(0deg, #09cc44 0%, #85fcaa 100%);
}
.font28 {
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  // margin-top: -6px;
}
.content-box {
  display: flex;
  height: calc(100% - 56px);
  margin: 0 10px;
  .data-left {
    width: 29.2%;
    height: 100%;
    .left-top {
      height: 36.33%;
      position: relative;
      .left-statistics{
        padding: 20px;
        width: 100%;
        height: 100%;
      }
    }
    .left-bottom {
      position: relative;
      height: 62%;
      margin-top: 12px;
    }
  }
  .data-centent {
    height: 100%;
    width: 41.6%;
    // height: 1020px;
    position: relative;
    .map-title {
      position: absolute;
      top: 100px;
      width: 100%;
      text-align: center;
      font-size: 16px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .map-time-box {
      position: absolute;
      width: 100%;
      top: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 99;
      .map-time-lable {
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #09fffc;
        margin-right: 14px;
      }
      .map-time-value {
        background: url("@/assets/images/top_input.png");
        background-size: 100% 100%;
        width: 120px;
        height: 34px;
        position: relative;
        :deep() .el-input__inner,
        :deep() .el-input__wrapper {
          background-color: #fff0;
          color: #fff;
          border: none;
          font-size: 16px;
          text-align: right;
          height: 34px;
          line-height: 34px;
          // padding: 0 25px 0 10px;
          box-shadow: none;
        }
        :deep() .el-input__suffix {
          color: #fff;
          font-size: 16px;
          line-height: 34px;
        }
        .map-icon-time {
          position: absolute;
          height: 18px;
          width: 18px;
          right: 10px;
          top: 8px;
        }
      }
      .map-time-btn {
        background: url("@/assets/images/btn_confirm.png");
        background-size: 100% 100%;
        width: 64px;
        height: 34px;
        margin-left: 20px;
        color: #09fffc;
        text-align: center;
        line-height: 34px;
        font-size: 16px;
        cursor: pointer;
      }
    }
    .type-box {
      position: absolute;
      top: 60px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      font-size: 18px;
      color: #fff;
      z-index: 99;
      .type-item {
        cursor: pointer;
        width: 140px;
        height: 38px;
        text-align: center;
        background-image: url("@/assets/images/type-item.png");
        &.active {
          background-image: url("@/assets/images/type-item-active.png");
        }
      }
    }
  }
  .data-right {
    width: 29.2%;
    height: 100%;
    .right-top {
      position: relative;
      height: 36.33%;
      .no-info {
        color: #fff;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 700;
      }
    }
    .right-center {
      width: 100%;
      // height: 14%;
      // margin: 10px 0;
      background-color: #000;
    }
    .right-bottom {
      margin-top: 14px;
      position: relative;
      height: 62%;
      .switch-box {
        position: absolute;
        bottom: 5px;
      }
    }
  }
}
.time-selector-box {
  display: none;
  // position: relative;
  padding: 35px 15px 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 5px;
  // padding: 0 14px;
  // gap: 14px;

  .time-selector-label {
    font-size: 16px;
    color: #fff;
    justify-self: flex-start;
    flex: 0 0 80px;
  }



  .time-picker-wrapper {
    flex: 0 1 295px;
    :deep() .el-input__wrapper,
    :deep() .el-date-editor.el-input,
    :deep() .el-date-editor.el-input__wrapper {
      width: 100%;
      height: 100%;
      background-color: transparent;
      background-color: #4F5C63;
    }
    :deep() .el-input__inner {
      color: #fff;
    }
  }

  .time-separator {
    flex: 0 0 20px;
    color: #fff;
    font-size: 16px;
    padding: 0 5px;
  }
}

.table-box {
  padding: 0 14px;
  padding-top: 40px;
  :deep() .el-table th.el-table__cell > .cell {
    padding: 0;
  }
  :deep()
    .el-table--enable-row-hover
    .el-table__body
    tr:hover
    > td.el-table__cell {
    background-color: rgba(133, 210, 249, 0.23922) !important;
  }
  :deep() .el-table td.el-table__cell {
    border-bottom: none;
    color: #fff;
    height: 60px;
    font-size: 16px;
  }
  :deep() .el-table tr {
    background-color: transparent;
    height: 45px;
  }
  :deep() .el-table {
    background-color: transparent;
    &::before {
      height: 0;
    }
  }
  :deep() .el-table th.el-table__cell {
    color: #00fff6;
    font-size: 16px;
    border-bottom: 0 solid #e8e8e8;
    text-align: center !important;
    padding: 6px 0 !important;
    background: linear-gradient(
      90deg,
      rgba(3, 68, 123, 0.6) 0%,
      #03447b 50%,
      rgba(3, 68, 123, 0.6) 100%
    );
  }
  :deep() .el-table__empty-text {
    color: #fff;
  }
  :deep() .el-table__body-wrapper {
    .el-table__body {
      width: 100% !important;
    }
    &::-webkit-scrollbar {
      width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 6px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(144, 147, 153, 0.5);
    }
    &::-webkit-scrollbar-track {
      border-radius: 5px;
      background: transparent;
    }
  }
}
.switch-box {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
  position: absolute;
  bottom: 14px;
  .switch-style {
    background: #3b718d;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    margin-right: 20px;
    cursor: pointer;
  }
  .bg {
    background: #8cd7ff;
  }
}
:deep() .clickRowStyl {
  background: linear-gradient(
    90deg,
    rgba(3, 68, 123, 0.35) 0%,
    #055192b3 50%,
    rgba(3, 68, 123, 0.35) 100%
  );
}
.bar-echart {
  width: 100%;
  height: 275px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.line-echart {
  width: 100%;
  height: 210px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.line-echart-title {
  text-align: center;
  font-size: 16px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
  margin: 18px 0 36px 0;
}
.pie-echart {
  display: flex;
  justify-content: space-around;
  margin: 35px 21px 0;
  .gesture{
    cursor: pointer;
  }
}
.powerCut {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
}
.top-title {
  position: absolute;
  color: #fff;
  font-size: 16px;
  width: 100%;
  text-align: center;
  > p {
    margin: 0;
    padding: 0;
  }
  .report-btn {
    position: absolute;
    top: -5px;
    right: 20%;
    z-index: 1;
    background: url("@/assets/images/btn_confirm.png");
    background-size: 100% 100%;
    // width: 64px;
    padding: 0 8px;
    height: 34px;
    color: #09fffc;
    text-align: center;
    line-height: 34px;
    font-size: 16px;
    cursor: pointer;
  }
}
.gutter-box {
  color: #fff;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  .city-box {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 20px;
    .alert-icon {
      height: 38px;
      width: 38px;
      margin-bottom: 6px;
      // animation: flash 2s ease-out 0s infinite;
      background: url("@/assets/images/alert-icon.png");
      background-size: 100% 100%;
    }
    .alert-icon-normal {
      height: 38px;
      width: 38px;
      margin-bottom: 6px;
      background: url("@/assets/images/alert-icon-normal.png");
      background-size: 100% 100%;
    }
    .alarm-blue {
      height: 38px;
      width: 38px;
      margin-bottom: 6px;
      background: url("@/assets/images/alarm-blue.png");
      background-size: 100% 100%;
    }
    .alarm-yellow {
      height: 38px;
      width: 38px;
      margin-bottom: 6px;
      background: url("@/assets/images/alarm-yellow.png");
      background-size: 100% 100%;
    }
     .alarm-orange {
      height: 38px;
      width: 38px;
      margin-bottom: 6px;
      background: url("@/assets/images/alarm-orange.png");
      background-size: 100% 100%;
    }
  }
}
@keyframes flash {
  0% {
    background: url("@/assets/images/alert-icon.png");
  }
  50% {
    background: url("@/assets/images/alarm-yellow.png");
  }
  100% {
    background: url("@/assets/images/alert-icon.png");
  }
}
.alert-box {
  // margin-bottom: 8px;
  display: flex;
  align-items: center;
  padding: 10px 5px;
  border-bottom: 1px solid #006273;
  img {
    min-width: 50px;
    margin-right: 8px;
  }
  .alert-title {
    // display: flex;
    margin-bottom: 4px;
    // img {
    //   height: 100%;
    //   margin-right: 5px;
    // }
    .early-warning-item-label {
      font-size: 15px;
    }
  }
}
.alert-bg {
  background: #85d2f913;
}
.back-btn {
  position: absolute;
  right: 18px;
  top: 20px;
  z-index: 100;
}
.alert-detail {
  width: 100%;
  color: #fff;
  padding: 0 20px;
  height: 100%;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(144, 147, 153, 0.5);
  }
  &::-webkit-scrollbar-track {
    border-radius: 5px;
    background: transparent;
  }
}
.alert-contnet {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 64px;
  padding-bottom: 10px;
  // display: flex;
  // align-items: flex-end;
}
.el-carousel__container{
  color: #fff;
  position: relative;
  height: 190px;
}
.noAlarm {
  text-align: center;
  padding-top: 15%;
  font-size: 16px;
}
.warringDetail {
  border-bottom: 3px solid #31649b;
  padding: 10px;
  font-size: 1.6vh;
}
.warringDetail div {
  margin: 2px 0;
  font-weight: 700;
}
.colorRed {
  color: #ff3737;
}
.marquee-box {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  padding: 5px 20px 0px 10px;
  img {
    height: 70px;
    margin-right: 10px;
  }
  .marquee-title {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    .marquee-item-label {
      .item-time {
        color: #00fff6;
      }
    }
  }
  .item-detail-label {
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    span {
      color: #00fff6;
    }
  }
}
.el-col-md-3-7 {
  width: 14.285%;
}
</style>
