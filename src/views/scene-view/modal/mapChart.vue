<template>
  <div style="width: 100%; height: 100%">
    <dv-loading v-show="loading"></dv-loading>
    <div v-show="!loading" style="width: 100%; height: 100%">
      <div class="back-btn">
        <el-button
          v-show="show"
          type="text"
          style="color: #04c5f3"
          icon="el-icon-refresh-right"
          @click="handleBack"
        >
          返回
        </el-button>
      </div>
      <transition mode="out-in">
        <div
          :class="className"
          :style="{ height: height, width: width }"
          style="margin-top: 50px;"
          ref="chart"
          v-show="isshow"
        />
      </transition>
      <el-button class="rule-btn" size="small" @click="handleRuleClick">
        规则配置
      </el-button>
      <div class="switch-box">
        <el-tooltip
          placement="top"
          effect="dark"
          v-for="(item, index) in switchList"
          :key="item"
        >
          <template #content>
            <span>{{ item }}情况</span>
          </template>
          <div
            class="switch-style"
            @click="handleSwitch(index)"
            :class="index === new_index ? 'bg' : ''"
          ></div>
        </el-tooltip>
      </div>
    </div>
    <!-- <time-modal ref="timeModal" @change="getTime"></time-modal> -->
  </div>
</template>

<script>
import * as echarts from "echarts";
import("echarts/theme/macarons"); // echarts theme
import resize from "./resize";
//import timeModal from "./timeModal";
//import { GetMap } from "@/api/sceneView/index";
import hebeiGeoJSON from './hebei/hebeigeo.json';

const cityMap = {
  '保定市': '保定市geo',
  '沧州市': '沧州市geo',
  // '承德市': '承德市geo',
  // '邯郸市': '邯郸市geo',
  // '廊坊市': '廊坊市geo',
  // '秦皇岛市': '秦皇岛市geo',
  // '邢台市': '邢台市geo',
  '雄安新区': '雄安新区geo',
}

const nameMap = [
  {
    api: '雄安新区',
    map: '雄安新区'
  }
]

export const Colors = [
  { label: 1, value: '#00558F' },
  { label: 2, value: '#D3D439' },
  { label: 3, value: '#E08224' },
  { label: 4, value: '#D26F6F' },
  { label: 5, value: '#D83A3A' },
]

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "86%",
    },
    chartData: {
      type: Array,
      required: true,
    },
    visualMapPieces: {
      type: Array,
      required: true,
    }
  },
  watch: {
    new_index: {
      deep: true,
      handler(val) {
        let data;
        let text = "";
        switch (val) {
          case 0:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 1:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 2:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 3:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 4:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 5:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 6:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          case 7:
            data = this.getCity(this.typeList[val]);
            text = this.getText(val);
            break;
          default:
            break;
        }
        this.setOptions(data);
        this.$emit("change", text);
      },
    },
    chartData: {
      deep: true,
      handler() {
        this.getHebeData();
        // if (val[0] && !val[0].county) {
        //   this.getHebeData();
        // } else {
        //   this.setHebeData(this.areaCode);
        // }
      },
    },
    visualMapPieces: {
      deep: true,
      handler() {
        let data = this.getCity(this.typeList[this.new_index]);
        this.setOptions(data);
      }
    }
  },
  data() {
    return {
      chart: null,
      xData: [],
      new_index: 0,
      switchList: [
        "停电",
        "欠压",
        "退服",
        "疑似退服",
        "离线",
        "温度过高",
        "烟雾/火灾",
        "水浸"
      ],
      show: false,
      city: "全省",
      areaCode: "460000",
      typeList: {
        0: "powerOutage",
        1: "underVoltage",
        2: "quitServer",
        3: "likeOutServer",
        4: "outLine",
        5: "wdgg",
        6: "fireSmoke",
        7: "waterImmersion"
      },
      loading: false,
      timeInterval: null,
      isshow: true,
      // layoutCenter: [],
      // layoutSize: ""
    };
  },
  created() {
    // this.timeInterval = setInterval(
    //   () => {
    //     this.indexChange();
    //   },
    //   localStorage.getItem("changeTime")
    //     ? localStorage.getItem("changeTime") * 1000
    //     : 10 * 1000
    // );
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  //components: { timeModal },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    if (this.timeInterval) clearInterval(this.timeInterval);
  },
  methods: {
    toMapCity (name) {
      const item = nameMap.find(x => x.api === name)
      return item?.map || `${name}市`
    },
    toApiCity (name) {
      const item = nameMap.find(x => x.map === name)
      return item?.api || name.replace('市', '')
    },
    getTime(time) {
      clearInterval(this.timeInterval);
      // this.timeInterval = setInterval(() => {
      //   this.indexChange();
      // }, time * 1000);
    },
    handleTime() {
      this.$refs.timeModal.initForm();
    },
    getText(index) {
      let textList = {
        0: `当前${this.city}停电情况`,
        1: `当前${this.city}欠压情况`,
        2: `当前${this.city}退服情况`,
        3: `当前${this.city}疑似退服情况`,
        4: `当前${this.city}离线情况`,
        5: `当前${this.city}温度过高情况`,
        6: `当前${this.city}烟雾/火灾情况`,
        7: `当前${this.city}水浸情况`
      };
      return textList[index];
    },
    handleBack() {
      this.$emit("setCity");
      this.city = "全省";
      this.areaCode = "460000";
      this.$emit("change", this.getText(this.new_index));
      this.show = false;
      this.loading = true;
    },
    getCity(type) {
      return this.chartData.map((v) => {
        const filedName = type
        const index = Object.values(this.typeList).indexOf(filedName)
        const cigType = this.visualMapPieces?.[index]?.[0]?.label?.indexOf('比例') > -1 ? '比例' : '次数'
        let cigTypeFlag = ''
        let fieldValue = v[type] || 0
        if (cigType === '比例') {
          cigTypeFlag = '%'
          fieldValue = v[`${type}Percent`] || 0
        }

        // api数据与地图的映射
        // api需要被动匹配geoJSON的name
        let item = {
          name: v.county ? v.county : this.toMapCity(v.city),
          value: fieldValue,
          list: v,
        };
        // 保留注释，后续下转功能要更换。
        // 这是一些特殊处理，后续下转功能要更换。
        // switch(item.name){
        //   case "澄迈市":
        //     item.name = '澄迈'
        //     break;
        //   case "乐东市":
        //     item.name = '乐东'
        //     break;
        //   case "三亚市":
        //     item.name = '三亚'
        //     break;
        //   case "万宁市":
        //     item.name = '万宁'
        //     break;
        //   case "琼海市":
        //     item.name = '琼海'
        //     break;
        //   case "文昌市":
        //     item.name = '文昌'
        //     break;
        //   case "海口市":
        //     item.name = '海口'
        //     break;
        //   case "儋州市":
        //     item.name = '儋州'
        //     break;
        //   case "东方市":
        //     item.name = '东方'
        //     break;
        // }
        item.label = {
          normal: {
            show: true,
            formatter: params => {
              if (!this.visualMapPieces) {
                return `${params.name}\n ${v[type]}`;
              }
              let str = `${params.name}\n${fieldValue}${cigTypeFlag}`;
              return str;
            }
          },
        };
        return item;
      });
    },
    indexChange() {
      this.isshow = false;
      setTimeout(() => {
        this.isshow = true;
      }, 1000);
      if (this.new_index >= 7) {
        this.new_index = 0;
      } else {
        ++this.new_index;
      }
    },
    handleSwitch(index) {
      this.isshow = false;
      setTimeout(() => {
        this.isshow = true;
      }, 1000);
      this.new_index = index;
    },
    async getHebeData() {
      this.loading = true;
      // let { result } = await GetMap({
      //   area: this.areaCode,
      // });
      let result = hebeiGeoJSON;
      if (this.city !== '全省' && this.city !== '') {
        await import(`./hebei/${cityMap[this.city] || this.city}.json`).then(res => {
          result = res.default
        }).catch(err => {
          console.error(err)
        })
      }

      // this.layoutCenter = ['141%', '252%'];
      // this.layoutSize = '580%';
      echarts.registerMap("HN", result);
      let data = this.getCity(this.typeList[this.new_index]);
      this.setOptions(data);
      this.loading = false;
      // this.initChart(result)
      // if (process.env.NODE_ENV === "development") {
      //   console.clear();
      // }
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart);
      echarts.registerMap('HN', hebeiGeoJSON)
      this.chart.on("click", (params) => {
        if (!params.data.list.county) {
          this.show = true;
          this.city = params.name;
          this.areaCode = params.data.list.areaCode;
          console.log(this.areaCode,"this.areaCode")
          this.$emit("setCity", params.data.list.city);
          this.$emit("change", this.getText(this.new_index));
        }

      });

      this.chart.on("mousemove", () => {
        clearInterval(this.timeInterval);
      });
      this.chart.on("mouseout", () => {
        if (this.timeInterval) clearInterval(this.timeInterval);
        // this.timeInterval = setInterval(
        //   () => {
        //     this.indexChange();
        //   },
        //   localStorage.getItem("changeTime")
        //     ? localStorage.getItem("changeTime") * 1000
        //     : 10 * 1000
        // );
      });
      let data = this.getCity(this.typeList[0]);
      this.setOptions(data);
    },
    setOptions(data) {
      let pieces = [];
      if (!this.visualMapPieces.length) {
        switch (this.new_index) {
          case 0:
            pieces = [
              { gte: 10, label: "停电占比>=10%", color: "#D83A3A" },
              {
                gte: 7,
                lt: 10,
                label: "7%=<停电占比<10%",
                color: "#D26F6F",
              },
              {
                gte: 5,
                lt: 7,
                label: "5%=<停电占比<7%",
                color: "#E08224",
              },
              {
                gte: 2,
                lt: 5,
                label: "2%=<停电占比<5%",
                color: "#D3D439",
              },
              { lt: 2, label: "停电占比<2%", color: "#00558F" },
            ];
            break;
          case 1:
            pieces = [
              { gte: 7, label: "欠压占比>=7%", color: "#D83A3A" },
              {
                gte: 5,
                lt: 7,
                label: "5%=<欠压占比<7%",
                color: "#D26F6F",
              },
              {
                gte: 3,
                lt: 5,
                label: "3%=<欠压占比<5%",
                color: "#E08224",
              },
              {
                gte: 1,
                lt: 3,
                label: "1%=<欠压占比<3%",
                color: "#D3D439",
              },
              { lt: 1, label: "欠压占比<1%", color: "#00558F" },
            ];
            break;
          case 2:
            pieces = [
              { gte: 15, label: "退服数>=15", color: "#D83A3A" },
              { gte: 10, lt: 15, label: "10=<退服数<15", color: "#D26F6F" },
              { gte: 5, lt: 10, label: "5=<退服数<10", color: "#E08224" },
              { gte: 1, lt: 5, label: "1=<退服数<5", color: "#D3D439" },
              { lt: 1, label: "退服数<1", color: "#00558F" },
            ];
            break;
          case 3:
            pieces = [
              { gte: 15, label: "疑似退服数>=15", color: "#D83A3A" },
              { gte: 10, lt: 15, label: "10=<疑似退服数<15", color: "#D26F6F" },
              { gte: 5, lt: 10, label: "5=<疑似退服数<10", color: "#E08224" },
              { gte: 1, lt: 5, label: "1=<疑似退服数<5", color: "#D3D439" },
              { lt: 1, label: "疑似退服数<1", color: "#00558F" },
            ];
            break;
          case 4:
            pieces = [
              { gte: 10, label: "离线占比>=10%", color: "#D83A3A" },
              {
                gte: 7,
                lt: 10,
                label: "7%=<离线占比<10%",
                color: "#D26F6F",
              },
              {
                gte: 5,
                lt: 7,
                label: "5%=<离线占比<7%",
                color: "#E08224",
              },
              {
                gte: 2,
                lt: 5,
                label: "2%=<离线占比<5%",
                color: "#D3D439",
              },
              { lt: 2, label: "离线占比<2%", color: "#00558F" },
            ];
            break;
          case 5:
            pieces = [
              { gte: 4, label: "温度过高占比>=4%", color: "#D83A3A" },
              {
                gte: 2,
                lt: 4,
                label: "2%=<温度过高占比<4%",
                color: "#D26F6F",
              },
              {
                gte: 1,
                lt: 2,
                label: "1%=<温度过高占比<2%",
                color: "#E08224",
              },
              {
                gte: 0.5,
                lt: 1,
                label: "0.5%=<温度过高占比<1%",
                color: "#D3D439",
              },
              { lt: 0.5, label: "温度过高占比<0.5%", color: "#00558F" },
            ];
            break;
          case 6:
            pieces = [
              { gte: 15, label: "烟雾/火灾数>=15", color: "#D83A3A" },
              { gte: 10, lt: 15, label: "10=<烟雾/火灾数<15", color: "#D26F6F" },
              { gte: 5, lt: 10, label: "5=<烟雾/火灾数<10", color: "#E08224" },
              { gte: 1, lt: 5, label: "1=<烟雾/火灾数<5", color: "#D3D439" },
              { lte: 0, label: "烟雾/火灾数<=0", color: "#00558F" },
            ];
            break;
          case 7:
            pieces = [
              { gte: 15, label: "水浸数>=15", color: "#D83A3A" },
              { gte: 10, lt: 15, label: "10=<水浸数<15", color: "#D26F6F" },
              { gte: 5, lt: 10, label: "5=<水浸数<10", color: "#E08224" },
              { gte: 1, lt: 5, label: "1=<水浸数<5", color: "#D3D439" },
              { lte: 0, label: "水浸数<=0", color: "#00558F" },
            ];
            break;
          default:
            break;
        }
      } else {
        pieces = this.visualMapPieces[this.new_index]
      }
      this.chart.setOption({
        tooltip: {
          trigger: "item",
          backgroundColor: "#03447b99",
          borderColor: "#00558F",
          formatter(params) {
            let list = params?.data?.list;

            let str = `
            <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">地市:</span>${
              list.county ? list.county : list.city
            }</div>
            <div>
              <span class="tool-lable">
              <!-- 站址总数: -->
              </span>
              <!-- ${list.statistics} -->
              </div>
            </div>
            <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前停电数:</span>${
              list.powerOutage
            }</div>
            <div><span class="tool-lable">停电占比:</span>${
              list.powerOutagePercent
            }%</div>
            </div>
            <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前欠压数:</span>${
              list.underVoltage
            }</div>
            <div><span class="tool-lable">欠压占比:</span>${
              list.underVoltagePercent
            }%</div>
            </div>
            <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前退服数:</span>${
              list.quitServer
            }</div>
            <div><span class="tool-lable">退服占比:</span>${
              list.quitServerPercent
            }%</div>
            </div>
            <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前疑似退服数:</span>${
              list.likeOutServer
            }</div>
            <div><span class="tool-lable">疑似退服占比:</span>${
              list.likeOutServerPercent
            }%</div>
            </div>
             <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前离线数:</span>${
              list.outLine
            }</div>
            <div><span class="tool-lable">离线占比:</span>${
              list.outLinePercent
            }%</div>
            </div>
             <div class="tool-box">
          <div class="tool-fist"><span class="tool-lable">当前温度过高数:</span>${
              list.wdgg
            }</div>
            <div><span class="tool-lable">温度过高占比:</span>${
              list.wdggPercent
            }%</div>
            </div>
             <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前烟雾/火灾数:</span>${
              list.fireSmoke
            }</div>
            <div class="tool-fist"><span class="tool-lable">烟雾/火灾占比:</span>${
              list.fireSmokePercent
            }%</div>
            </div>
             <div class="tool-box">
            <div class="tool-fist"><span class="tool-lable">当前水浸数:</span>${
              list.waterImmersion
            }</div>
            <div><span class="tool-lable">水浸占比:</span>${
              list.waterImmersionPercent
            }%</div>
            </div>
            `;
            return str;
          },
        },
        visualMap: {
          type: "piecewise", // 类型为分段型
          top: "bottom",
          right: 26,
          splitNumber: 6,
          seriesIndex: [0],
          itemWidth: 20, // 每个图元的宽度
          //itemGap: 4, // 每两个图元之间的间隔距离，单位为px
          pieces: pieces,
          orient: "vertical",
          align: "left",
          itemGap: 20,
          textStyle: {
            fontSize: 14,
            color: "#fff",
          },

        },
        series: [
          {
            type: "map",
            map: "HN",
            // layoutCenter: ['141%', '252%'],//中心位置
            // layoutSize:'580%',//大小
            // layoutCenter: this.layoutCenter,
            // layoutSize: this.layoutSize,
            label: {
              show: true,
              normal: {
                textStyle: {
                  fontSize: 12,
                  color: "#fff",
                },
              },
            },
            select: {
              label: {
                color: "#fff",
              },
              itemStyle: "",
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                },
                borderWidth: 2,
                borderColor: "#00ffff",
                areaColor: "#00E8F9",
              },
              emphasis: {
                label: {
                  show: false,
                  textStyle: {
                    color: "#fff",
                  },
                },
                areaColor: "#3066ba",
              },
            },

            data: data,
          },
        ],
      });
    },
    handleRuleClick () {
      this.$emit('on-rule-click', {
        alarmType: this.switchList[this.new_index]
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.switch-box {
  width: 100%;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
  position: absolute;
  bottom: 14px;
  .switch-style {
    background: #3b718d;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    margin-right: 20px;
    cursor: pointer;
  }
  .bg {
    background: #8cd7ff;
  }
}
:deep() .tool-box {
  display: flex;
  font-size: 16px;
  color: #fff;
  line-height: 30px;
  div {
    flex: 1;
  }
  .tool-lable {
    display: inline-block;
    width: 120px;
    text-align: right;
  }
  .tool-fist {
    margin-right: 20px;
  }
}
.back-btn {
  position: absolute;
  right: 8px;
  top: 28px;
  z-index: 100;
}
.icon-right {
  color: #fff;
  cursor: pointer;
}

.v-enter,
.v-leave-to {
  opacity: 0;
  transform: translateX(150px);
}

.v-enter-active,
.v-leave-active {
  transition: all 0.5s ease;
}

.rule-btn {
  position: absolute;
  bottom: 50px;
  right: 8px;
  background-color: rgba(0, 127, 181, 0.4);
  color: #fff;
  border: 1px solid rgba(0, 127, 181, 1);
}
</style>
