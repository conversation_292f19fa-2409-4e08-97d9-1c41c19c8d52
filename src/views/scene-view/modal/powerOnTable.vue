<template>
  <el-dialog
    title="已发电告警信息"
    class="dialog-scenc"
    :visible.sync="visible"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-if="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'地市'"
          v-model="listQuery.city"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'区县'"
          v-model="listQuery.county"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'站址名称'"
          v-model="listQuery.station_name"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'运维ID'"
          v-model="listQuery.station_id"
        >
        </el-input>
        <el-button
          class="filter-item search-btn"
          style="margin-left: 8px"
          size="mini"
          icon="el-icon-search"
          @click="handleFilter"
          >搜索</el-button
        >
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        icon="el-icon-download"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="510"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="序号" type="index" width="80px" align="center">
          <template slot-scope="scope">
            <span>{{
              (listQuery.pageNo - 1) * listQuery.pageSize + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="所属地市"
          min-width="150px"
          prop="cityName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属区域"
          min-width="150px"
          prop="areaName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址名称"
          min-width="150px"
          prop="stationName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="站址编码"
          min-width="150px"
          prop="stationCode"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="运维监控系统id"
          min-width="150px"
          prop="amsId"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警历时"
          min-width="150px"
          prop="arriveTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="共享运营商"
          min-width="150px"
          prop="shareUnit"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="发电运营商"
          min-width="150px"
          prop="generateOperators"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警时间"
          min-width="150px"
          prop="warningDate"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="发电原因"
          min-width="150px"
          prop="generateReason"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警来源"
          min-width="150px"
          prop="warnCome"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="发电状态"
          min-width="150px"
          prop="generateState"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="代维单位"
          min-width="150px"
          prop="generateOfficeName"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="工单创建时间"
          min-width="150px"
          prop="generateDate"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="采集模块"
          min-width="150px"
          prop="collectorCode"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange"
    />
  </el-dialog>
</template>

<script>
import { getPowerOnDetail } from "@/api/sceneView/index";
import { parseTime } from '@/utils/old-index'
export default {
  name: "modalForm",
  data() {
    return {
      title: "",
      visible: false,
      total: 0,
      listLoading: false,
      tableList: [],
      listQuery: {
        // 查询条件
        pageNo: 1,
        pageSize: 20,
        alarmType: "",
        city: "",
        county: "",
        station_name: "",
        station_id: "",
      },
      exportLoading: false
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    async handleExport(){
      this.exportLoading = true;
      const exportList = await this.getExportList();
      this.handleXls(exportList);
      this.exportLoading = false;
    },
    async getExportList () {
      let { result } = await getPowerOnDetail({
        ...this.listQuery,
        pageSize: this.total
      });
      return result.records || [];
    },
    handleXls(dataList){
      try {
        const theader = '所属地市,所属区域,站址名称,站址编码,运维监控系统id,共享运营商,发电运营商,告警时间,发电原因,告警来源,发电状态,代维单位,工单创建时间,采集模块';
        let list = dataList.map(item => {
        //增加\t为了不让表格显示科学计数法或者其他格式
        return '\n'+ ([
          item.cityName,
          item.areaName,
          item.stationName,
          item.stationCode + '\t',
          item.amsId + '\t',
          item.shareUnit,
          item.generateOperators,
          item.warningDate + '\t',
          item.generateReason,
          item.warnCome,
          item.generateState,
          item.generateOfficeName,
          item.generateDate + '\t',
          item.collectorCode + '\t',
        ].join(','))
        })
        const str = theader + list.join('')
        let uri = 'data:text/xls;charset=utf-8,\ufeff' + encodeURIComponent(str);
        //通过创建a标签实现
        var link = document.createElement("a");
        link.href = uri;
        //对下载的文件命名
        link.download =  "发电告警信息" + '_' + parseTime(new Date()) + ".xls";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (e) {
        this.$message.error('导出失败');
        console.error(e);
      }
    },
    handleOpen() {},
    handleCurrentChange(val) {
      this.listQuery.pageNo = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let { result } = await getPowerOnDetail(this.listQuery);
      this.total = result.total;
      // console.log(result,"result")
      this.tableList = result.records;
      this.listLoading = false;
    },
    addRowColor(record, index) {
      return index % 2 != 0 ? "rowStyl" : "";
    },
    initForm(data) {
      this.title = data.alarmType + "告警信息";
      this.listQuery.pageNo = 1;
      this.listQuery.pageSize = 20;
      this.listQuery.alarmType = data.alarmType;
      this.listQuery.city = data.city;
      this.visible = true;
      this.getList();
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    close() {
      this.listQuery = {
        // 查询条件
        pageNo: 1,
        pageSize: 20,
        alarmType: "",
        city: "",
        county: "",
        station_name: "",
        station_id: "",
      };
      this.visible = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },
    handleClose() {
      this.close();
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 1px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
.dialog-scenc {
  :deep() .el-dialog {
    background-color: #065e89;
    opacity: 0.9;
    .el-dialog__header {
      height: 55px;
      line-height: 55px;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #059ec0;
      overflow: hidden;
      padding: 0 20px !important;
      .el-dialog__title {
        color: #fff;
      }
    }
    .el-dialog__body {
      padding: 10px 20px 0 20px;
      .rowStyle {
        background: #084969 !important;
      }
      .pagination-container {
        background: transparent;
        border-left: none;
        border-right: none;
        display: flex;
        justify-content: flex-end;
        .el-pagination__total {
          color: #fff;
        }
        .el-pagination__jump {
          color: #fff;
        }
      }
      .table-box {
        .sticky {
          background-color: rgba(144, 147, 153, 0.5);
          .el-input__inner {
            background: linear-gradient(0deg, #385fb866, #2238690d);
            border: 2px solid #059ec0;
            color: #82bee9;
            margin-bottom: 6px;
          }
          .search-btn {
            background: #059ec0;
            color: #fff;
            border: 1px solid #059ec0;
            margin-top: -3px;
          }
        }
        .el-table--enable-row-hover
          .el-table__body
          tr:hover
          > td.el-table__cell {
          background-color: rgba(133, 210, 249, 0.23922) !important;
        }
        .el-table td.el-table__cell {
          border-bottom: 1px solid #059ec0;
          color: #fff;
          height: 45px;
          font-size: 16px;
        }
        .el-table tr {
          background-color: transparent;
          height: 45px;
        }
        .el-table {
          background-color: transparent;
          &::before {
            height: 0;
          }
        }
        .el-table th.el-table__cell {
          background: #084969;
          color: #d2e7ff;
          font-size: 17px;
          font-weight: 700;
          border-bottom: 1px solid #059ec0;
        }
        .el-table__empty-text {
          color: #fff;
        }
        .el-table--border {
          border: 1px solid #059ec0;
        }
        .el-table--border .el-table__cell {
          border-right: 1px solid #059ec0;
        }
        .el-table--border::after {
          width: 0;
        }
        .el-table__body-wrapper {
          .el-table__body {
            width: 100% !important;
          }
          &::-webkit-scrollbar {
            width: 6px;
            height: 12px;
          }
          &::-webkit-scrollbar-thumb {
            // border-radius: 6px;
            background: rgba(144, 147, 153, 0.5);
            border-radius: 0;
            -webkit-box-shadow: inset 0 0 5px #0003;
          }
          &::-webkit-scrollbar-track {
            background: transparent;
            -webkit-box-shadow: inset 0 0 5px #0003;
            border-radius: 0;
          }
        }
      }
    }
  }
}
</style>
