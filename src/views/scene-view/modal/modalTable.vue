<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    :visible.sync="visible"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-if="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'地市'"
          v-model="listQuery.city"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'区县'"
          v-model="listQuery.county"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'站址名称'"
          v-model="listQuery.station_name"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'运维ID'"
          v-model="listQuery.station_id"
        >
        </el-input>
        <el-button
          class="filter-item search-btn"
          style="margin-left: 8px"
          size="mini"
          icon="el-icon-search"
          @click="handleFilter"
          >搜索</el-button
        >
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        icon="el-icon-download"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="510"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="序号" type="index" width="80px" align="center">
          <template slot-scope="scope">
            <span>{{
              (listQuery.pageNo - 1) * listQuery.pageSize + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="所属市"
          min-width="150px"
          prop="city"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属区县"
          min-width="150px"
          prop="county"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警等级"
          min-width="96px"
          prop="alarmGrade"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          label="告警名称"
          min-width="150px"
          prop="alarmName"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          label="告警详情"
          min-width="150px"
          prop="alarmDetails"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          label="站址名称"
          min-width="150px"
          prop="stationName"
          align="center"
          show-overflow-tooltip
        />

        <el-table-column
          label="告警发生时间"
          min-width="150px"
          prop="alarmTime"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="告警历时(分钟)"
          min-width="150px"
          prop="alarmLast"
          align="center"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          label="站址编码"
          min-width="150px"
          prop="stationCode"
          align="center"
          show-overflow-tooltip
        /> -->
        <el-table-column
          label="运维ID"
          min-width="150px"
          prop="stationId"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="所属运营商"
          min-width="150px"
          prop="theirOperators"
          align="center"
          show-overflow-tooltip
        />

        <!-- <el-table-column
          label="告警流水号ID"
          min-width="150px"
          prop="alarmId"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="是否超时"
          min-width="150px"
          prop="isTimeout"
          align="center"
          show-overflow-tooltip
        /> -->
        <el-table-column
          label="告警来源"
          min-width="150px"
          prop="alarmSource"
          align="center"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          prop="malfunctionCode"
          min-width="150px"
          label="故障单编号"
          align="center"
          show-overflow-tooltip
        /> -->
        <!-- <el-table-column
          prop="createTime"
          min-width="150px"
          label="创建时间"
          align="center"
          show-overflow-tooltip
        /> -->
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange"
    />
  </el-dialog>
</template>

<script>
import { getAlarmDataDetail } from "@/api/sceneView/index";
import { parseTime } from '@/utils/old-index'
export default {
  name: "modalForm",
  data() {
    return {
      title: "",
      visible: false,
      total: 0,
      listLoading: false,
      tableList: [],
      listQuery: {
        // 查询条件
        pageNo: 1,
        pageSize: 20,
        alarmType: "",
        city: "",
        county: "",
        station_name: "",
        station_id: "",
      },
      exportLoading: false
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    async handleExport(){
      this.exportLoading = true;
      const exportList = await this.getExportList();
      this.handleXls(exportList);
      this.exportLoading = false;
    },
    async getExportList () {
      let { result } = await getAlarmDataDetail({
        ...this.listQuery,
        pageSize: this.total
      });
      return result.records || [];
    },
    handleXls(dataList){
      try {
        const theader = '所属市,所属区县,告警等级,告警名称,告警详情,站址名称,告警发生时间,告警历时(分钟),运维ID,所属运营商,告警来源';
        let list = dataList.map(item => {
        //增加\t为了不让表格显示科学计数法或者其他格式
        return '\n'+ ([
          item.city,
          item.county,
          item.alarmGrade,
          item.alarmName,
          item.alarmDetails,
          item.stationName,
          item.alarmTime + '\t',
          item.alarmLast + '\t',
          item.stationId + '\t',
          item.theirOperators,
          item.alarmSource,
        ].join(','))
        })
        const str = theader + list.join('')
        let uri = 'data:text/xls;charset=utf-8,\ufeff' + encodeURIComponent(str);
        //通过创建a标签实现
        var link = document.createElement("a");
        link.href = uri;
        //对下载的文件命名
        link.download =  this.title + '_' + parseTime(new Date()) + ".xls";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (e) {
        this.$message.error('导出失败');
        console.error(e);
      }
    },
    handleOpen() {},
    handleCurrentChange(val) {
      this.listQuery.pageNo = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let { result } = await getAlarmDataDetail(this.listQuery);
      this.total = result.total;
      this.tableList = result.records;
      this.listLoading = false;
    },
    addRowColor(record, index) {
      return index % 2 != 0 ? "rowStyl" : "";
    },
    initForm(data) {
      this.title = data.alarmType + "告警信息";
      this.listQuery.pageNo = 1;
      this.listQuery.pageSize = 20;
      this.listQuery.alarmType = data.alarmType;
      this.listQuery.city = data.city;
      this.visible = true;
      this.getList();
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    close() {
      this.listQuery = {
        // 查询条件
        pageNo: 1,
        pageSize: 20,
        alarmType: "",
        city: "",
        county: "",
        station_name: "",
        station_id: "",
      };
      this.visible = false;
    },
    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },
    handleClose() {
      this.close();
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 1px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
.dialog-scenc {
  :deep() .el-dialog {
    background-color: #065e89;
    opacity: 0.9;
    .el-dialog__header {
      height: 55px;
      line-height: 55px;
      border-radius: 4px 4px 0 0;
      border-bottom: 1px solid #059ec0;
      overflow: hidden;
      padding: 0 20px !important;
      .el-dialog__title {
        color: #fff;
      }
    }
    .el-dialog__body {
      padding: 10px 20px 0 20px;
      .rowStyle {
        background: #084969 !important;
      }
      .pagination-container {
        background: transparent;
        border-left: none;
        border-right: none;
        display: flex;
        justify-content: flex-end;
        .el-pagination__total {
          color: #fff;
        }
        .el-pagination__jump {
          color: #fff;
        }
      }
      .table-box {
        .sticky {
          background-color: rgba(144, 147, 153, 0.5);
          .el-input__inner {
            background: linear-gradient(0deg, #385fb866, #2238690d);
            border: 2px solid #059ec0;
            color: #82bee9;
            margin-bottom: 6px;
          }
          .search-btn {
            background: #059ec0;
            color: #fff;
            border: 1px solid #059ec0;
            margin-top: -3px;
          }
        }
        .el-table--enable-row-hover
          .el-table__body
          tr:hover
          > td.el-table__cell {
          background-color: rgba(133, 210, 249, 0.23922) !important;
        }
        .el-table td.el-table__cell {
          border-bottom: 1px solid #059ec0;
          color: #fff;
          height: 45px;
          font-size: 16px;
        }
        .el-table tr {
          background-color: transparent;
          height: 45px;
        }
        .el-table {
          background-color: transparent;
          &::before {
            height: 0;
          }
        }
        .el-table th.el-table__cell {
          background: #084969;
          color: #d2e7ff;
          font-size: 17px;
          font-weight: 700;
          border-bottom: 1px solid #059ec0;
        }
        .el-table__empty-text {
          color: #fff;
        }
        .el-table--border {
          border: 1px solid #059ec0;
        }
        .el-table--border .el-table__cell {
          border-right: 1px solid #059ec0;
        }
        .el-table--border::after {
          width: 0;
        }
        .el-table__body-wrapper {
          .el-table__body {
            width: 100% !important;
          }
          &::-webkit-scrollbar {
            width: 6px;
            height: 12px;
          }
          &::-webkit-scrollbar-thumb {
            // border-radius: 6px;
            background: rgba(144, 147, 153, 0.5);
            border-radius: 0;
            -webkit-box-shadow: inset 0 0 5px #0003;
          }
          &::-webkit-scrollbar-track {
            background: transparent;
            -webkit-box-shadow: inset 0 0 5px #0003;
            border-radius: 0;
          }
        }
      }
    }
  }
}
</style>
