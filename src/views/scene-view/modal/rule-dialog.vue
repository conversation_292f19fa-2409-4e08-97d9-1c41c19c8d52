<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="dialog-block-content">
      <div class="rule-config">
        <el-radio v-model="formData.cigType" label="次数">输入次数</el-radio>
        <el-radio v-model="formData.cigType" label="比例">输入比例</el-radio>
      </div>
      <div class="value-config">

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[0].value }"></div>
          <div class="">
            当{{ formData.alarmType }}&lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword1" size="mini" />{{ formData.cigType }}。
          </div>
        </div>

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[1].value }"></div>
          <div class="">
            当{{ formData.alarmType }}≤<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword2" :min="formData.keyword1" size="mini" />{{ formData.cigType }};
            &lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword3"
            :min="formData.cigType === '次数' ? formData.keyword2 + 1 : formData.keyword2" size="mini" />{{ formData.cigType }}。
          </div>
        </div>

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[2].value }"></div>
          <div class="">
            当{{ formData.alarmType }}≤<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword4"
            :min="formData.cigType === '次数' ? formData.keyword3 : formData.keyword3" size="mini" />{{ formData.cigType }};
            &lt;<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword5"
            :min="formData.cigType === '次数' ? formData.keyword4 + 1 : formData.keyword4" size="mini" />{{ formData.cigType }}。
          </div>
        </div>

        <div class="config-bar">
          <div class="color" :style="{ backgroundColor: colors[3].value }"></div>
          <div class="">
            当{{ formData.alarmType }}≥<el-input-number class="el-input-num" :controls="false" v-model="formData.keyword6" :min="formData.keyword7" size="mini" />。
          </div>
        </div>
      </div>
      <div class="btn-bar">
        <el-button class="submit-btn" size="small" @click="handleRuleClick">
          保存
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { updateRuleConfig } from "@/api/sceneView/index";
import { Colors } from "./mapChart.vue";

export default {
  name: "rule-dialog",
  data() {
    return {
      title: "",
      visible: false,
      loading: false,
      colors: Colors,
      formData: {},
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    handleOpen() {},
    initForm(formData) {
      this.title = formData.alarmType + "规则配置";
      this.visible = true;
      this.formData = formData
    },
    async handleSubmit() {
      let keywords = ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5", "keyword6"];
      for (let i = 1; i < keywords.length; i++) {
        let currentKeyword = keywords[i];
        let previousKeyword = keywords[i - 1];

        if (this.formData[currentKeyword] < this.formData[previousKeyword]) {
          this.$message({
            message: '输入的值不合法',
            type: 'warning'
          });
          return false;
        }
      }
      const res = await updateRuleConfig({
        ...this.formData
      })
      if (res) {
        this.$message({
          message: '修改成功',
        })
        this.$emit("update:isShow", false)
        this.$emit("on-update", this.formData)
      }
    },
    async handleRuleClick () {
      this.loading = true;
      let keywords = ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5", "keyword6"];
      for (let i = 1; i < keywords.length; i++) {
        let currentKeyword = keywords[i];
        let previousKeyword = keywords[i - 1];

        if (this.formData[currentKeyword] < this.formData[previousKeyword]) {
          this.$message({
            message: '输入的值不合法',
            type: 'warning'
          });
          return false;
        }
      }
      let { code } = await updateRuleConfig({
        ...this.formData
      })
      this.loading = false
      if (code === 200) {
        this.$message({
          message: '修改成功',
        })
        this.$emit("on-update", this.formData)
        this.close()
      }
    },
    close() {
      this.formData = {};
      this.visible = false;
    },
    handleClose() {
      this.close();
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
  :deep() .filter-item {
    margin-bottom: 0 !important;
  }
}
:deep() .el-input__wrapper {
  background-color: transparent;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 0px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
