import request from '@/utils/request'

// 告警统计列表/api/BigScreen/BigScreen/GetMap
export function getAlarmCityData(params) {
  return request({
    url: '/BigScreen/BigScreen/getAlarmCityData',
    method: 'get',
    params
  })
}

// 告警统计
export function getAlarmData(params) {
  return request({
    url: '/BigScreen/BigScreen/getAlarmData',
    method: 'get',
    params
  })
}

// 地图json
export function GetMap(params) {
  return request({
    url: '/BigScreen/BigScreen/GetMap',
    method: 'get',
    params
  })
}

// 天气预报
export function getWeatherData(params) {
  return request({
    url: '/BigScreen/BigScreen/getWeatherData',
    method: 'get',
    params
  })
}

// 告警详情
export function getAlarmDataDetail(params) {
  return request({
    url: '/BigScreen/BigScreen/getAlarmDataDetail',
    method: 'get',
    params
  })
}

// 倒计时
export function BigScreenCountdown(params) {
  return request({
    url: '/BigScreen/BigScreen/countdown',
    method: 'get',
    params
  })
}

// 离线告警
export function getFsuDetail(params) {
  return request({
    url: '/BigScreen/BigScreen/getFsuDetail',
    method: 'get',
    params
  })
}

// 疑似退服
export function GetLikeLineOffs(params) {
  return request({
    url: '/BigScreen/BigScreen/GetLikeLineOffs',
    method: 'get',
    params
  })
}

// 火灾预警
export function GetFireWarnings(params) {
  return request({
    url: '/BigScreen/BigScreen/GetFireWarnings',
    method: 'get',
    params
  })
}

// 铁塔历史工单及时率列表
export function getTTHistoryOrderData(params) {
  return request({
    url: '/BigScreen/BigScreen/getTTHistoryOrderData',
    method: 'get',
    params
  })
}

// 发电告警详情
export function getPowerOnDetail(params) {
  return request({
    url: '/BigScreen/BigScreen/getPowerOnDetail',
    method: 'get',
    params
  })
}

// 获取大屏热力图告警参数配置
export function getRuleConfig(params) {
  return request({
    url: '/ChttBigScreenConfigTbls/Load',
    method: 'get',
    params
  })
}

// 修改大屏热力图告警参数配置
export function updateRuleConfig(params) {
  return request({
    url: '/ChttBigScreenConfigTbls/Update',
    method: 'post',
    data: params
  })
}

// 台风监控大屏

// 倒计时
export function TyphoonCountdown(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/countdown',
    method: 'get',
    params
  })
}

// 告警统计
export function TyphoonGetAlarmData(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getAlarmData',
    method: 'get',
    params
  })
}

// 获取全省地市告警数
export function TyphoonGetAlarmCityData(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getAlarmCityData',
    method: 'get',
    params
  })
}

// 告警详细
export function TyphoonGetAlarmDataDetail(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getAlarmDataDetail',
    method: 'get',
    params
  })
}

//停电告警详情
export function TyphoonGetPowerFailureAlarmDataDetail(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getPowerFailureAlarmDataDetail',
    method: 'get',
    params
  })
}

// FSU发电告警详细
export function TyphoonGetFsuDetail(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getFsuDetail',
    method: 'get',
    params
  })
}

// 天气预报
export function TyphoonGetWeatherData(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getWeatherData',
    method: 'get',
    params
  })
}

// 运营商全省告警统计
export function TyphoonGetAlarmOperatorData(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getAlarmOperatorData',
    method: 'get',
    params
  })
}

// 获取保障时间
export function TyphooGetEmergencyDate(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/getEmergencyDate',
    method: 'get',
    params
  })
}

// 修改保障时间
export function TyphooUpdateEmergencyDate(params) {
  return request({
    url: '/BigScreen/TyphoonBigScreen/updateEmergencyDate',
    method: 'post',
    data: params
  })
}

// ==================== 告警统计 接口 ====================

/**
 * 规则配置-查看详情
 * @param {Object} params - 请求参数
 * @param {string} params.alarmType - 告警类型：停电、欠压、退服、疑似退服、离线、温度过高、烟雾/火灾、水浸
 * @param {string} params.cigType - 区间类型-比例、次数
 * @returns {Promise<Object>} 返回地图颜色规则配置数据，包含：
 * - alarmType: 告警类型
 * - cigType: 区间类型-比例、次数
 * - id: ID
 * - keyword1-keyword8: keyword 1 到 8，对应页面从上到下、从左到右
 */
export function getAlarmPropDetail(params) {
  return request({
    url: '/statistics/alarm/prop/detail',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 规则配置 - 修改
 * @param {Object} data - 地图颜色规则配置修改实体
 * @param {string} [data.alarmType] - 告警类型
 * @param {string} [data.cigType] - 区间类型-比例、次数
 * @param {number} [data.id] - ID
 * @param {string} [data.keyword1] - keyword 1 到 8，对应页面从上到下、从左到右
 * @param {string} [data.keyword2] - keyword2
 * @param {string} [data.keyword3] - keyword3
 * @param {string} [data.keyword4] - keyword4
 * @param {string} [data.keyword5] - keyword5
 * @param {string} [data.keyword6] - keyword6
 * @param {string} [data.keyword7] - keyword7
 * @param {string} [data.keyword8] - keyword8
 * @returns {Promise<string>} 返回操作结果
 */
export function updateAlarmProp(data) {
  return request({
    url: '/statistics/alarm/prop/update',
    method: 'put',
    data
  }).then(response => response.result)
}

/**
 * 全省各地市告警数
 * @param {Object} params - 请求参数
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Array>} 返回全省各地市告警数列表，每个对象包含：
 * - city: 地市
 * - fireSmoke: 烟雾/火灾
 * - fireSmokePercent: 烟雾/火灾百分比(小数点形式)
 * - likeOutServer: 疑似退服
 * - likeOutServerPercent: 疑似退服百分比(小数点形式)
 * - outLine: 离线
 * - outLinePercent: 离线百分比(小数点形式)
 * - powerOutage: 停电
 * - powerOutagePercent: 停电百分比(小数点形式)
 * - quitServer: 退服
 * - quitServerPercent: 退服百分比(小数点形式)
 * - siteCount: 站址总数
 * - underVoltage: 欠压
 * - underVoltagePercent: 欠压百分比(小数点形式)
 * - waterImmersion: 水浸
 * - waterImmersionPercent: 水浸百分比(小数点形式)
 * - wdgg: 温度过高
 * - wdggPercent: 温度过高百分比(小数点形式)
 */
export function getCityAlarmStatistics(params) {
  return request({
    url: '/statistics/city/alarm',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 点击地图某地市查看各区县告警数
 * @param {Object} params - 请求参数
 * @param {string} params.city - 地市
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Array>} 返回各区县告警数列表，每个对象包含：
 * - county: 区县
 * - fireSmoke: 烟雾/火灾
 * - fireSmokePercent: 烟雾/火灾百分比(小数点形式)
 * - likeOutServer: 疑似退服
 * - likeOutServerPercent: 疑似退服百分比(小数点形式)
 * - outLine: 离线
 * - outLinePercent: 离线百分比(小数点形式)
 * - powerOutage: 停电
 * - powerOutagePercent: 停电百分比(小数点形式)
 * - quitServer: 退服
 * - quitServerPercent: 退服百分比(小数点形式)
 * - siteCount: 站址总数
 * - underVoltage: 欠压
 * - underVoltagePercent: 欠压百分比(小数点形式)
 * - waterImmersion: 水浸
 * - waterImmersionPercent: 水浸百分比(小数点形式)
 * - wdgg: 温度过高
 * - wdggPercent: 温度过高百分比(小数点形式)
 */
export function getCountyAlarmStatistics(params) {
  return request({
    url: '/statistics/county/alarm',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 全省告警统计
 * @param {Object} params - 请求参数
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Object>} 返回全省告警统计数据，包含：
 * - fireSmoke: 烟雾/火灾
 * - likeOutServer: 疑似退服
 * - outLine: 离线
 * - powerOutage: 停电
 * - quitServer: 退服
 * - underVoltage: 欠压
 * - waterImmersion: 水浸
 * - wdgg: 温度过高
 */
export function getProvinceAlarmStatistics(params) {
  return request({
    url: '/statistics/province/alarm',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 全省告警统计列表- 疑似退服、烟雾-火灾告警、水浸
 * @param {Object} params - 请求参数
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Array>} 返回疑似退服、烟雾/火灾告警、水浸类型的列表，每个对象包含：
 * - city: 所属市
 * - county: 所属区县
 * - faultAlarmTime: 故障发生时间
 * - faultDetails: 故障详情
 * - faultGrade: 故障等级
 * - faultLast: 故障历时（分钟）
 * - faultName: 故障名称
 * - faultWaterId: 故障流水ID
 * - operationId: 站址运维ID
 * - siteCode: 站址资源编码
 * - siteName: 站址名称
 */
export function getProvinceAlarmActiveList(params) {
  return request({
    url: '/statistics/province/alarm/active/list',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 全省告警统计列表- 离线
 * @param {Object} params - 请求参数
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Array>} 返回离线类型的列表，每个对象包含：
 * - city: 市
 * - county: 国家行政区县
 * - deviceModel: 设备型号
 * - dwCompany: 代维公司
 * - fsuHardware: FSU硬件厂家
 * - fsuManufacturer: FSU软件厂家
 * - fsuName: FSU名称
 * - fsuOperationId: FSU运维ID
 * - fsuSoftware: FSU软件版本
 * - head: 负责人
 * - headPhone: 维护人电话
 * - networkStandard: 网络制式
 * - offlineTime: 离线时间
 * - operationId: 站址运维ID
 * - power: 功率
 * - region: 区域(铁塔自划)
 * - siteCode: 站址编码
 * - siteName: 站址名称
 */
export function getProvinceAlarmFsuList(params) {
  return request({
    url: '/statistics/province/alarm/fsu/list',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 全省告警统计列表- 停电、欠压、退服、高温告警
 * @param {Object} params - 请求参数
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Array>} 返回停电、欠压、退服、高温告警类型的列表，每个对象包含：
 * - alarmDetails: 告警详情
 * - alarmFrom: 告警来源
 * - alarmGrade: 告警等级
 * - alarmLast: 告警历时(分钟)
 * - alarmName: 告警名称
 * - alarmTime: 告警发生时间
 * - alarmWaterId: 告警流水号ID
 * - city: 所属市
 * - county: 所属区县
 * - faultCode: 故障单编号
 * - isTimeout: 是否超时
 * - operationId: 运维Id
 * - operator: 所属运营商
 * - siteCode: 站址编码
 * - siteName: 站址名称
 * - updateTime: 创建时间
 */
export function getProvinceAlarmPyAlarmList(params) {
  return request({
    url: '/statistics/province/alarm/pyAlarm/list',
    method: 'get',
    params
  }).then(response => response.result)
}

/**
 * 点击地图某地市查看告警统计(点击地图左上全省告警统计数据联动)
 * @param {Object} params - 请求参数
 * @param {string} params.city - 地市
 * @param {string} params.type - 运营商类型：铁塔、移动、联通、电信
 * @returns {Promise<Object>} 返回告警统计数据，包含：
 * - fireSmoke: 烟雾/火灾
 * - likeOutServer: 疑似退服
 * - outLine: 离线
 * - powerOutage: 停电
 * - quitServer: 退服
 * - underVoltage: 欠压
 * - waterImmersion: 水浸
 * - wdgg: 温度过高
 */
export function getProvinceCityAlarmStatistics(params) {
  return request({
    url: '/statistics/province/city/alarm',
    method: 'get',
    params
  }).then(response => response.result)
}

// ==================== 大屏单站告警量Top50 接口 ====================

/**
 * 单站告警统计 - 导出
 * @param {Object} params - 请求参数
 * @param {string} [params.alarmGrade] - 告警等级
 * @param {string} [params.alarmName] - 告警名称
 * @param {string} [params.city] - 市(无值为地市维度，有值为区县维度)
 * @param {string} [params.county] - 区县
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编号
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商(不传为全量)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteCode] - 站址编码
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise} 返回导出结果
 */
export function exportStationAlarm(params) {
  return request({
    url: '/oa/station_alarm/export',
    method: 'get',
    params,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },

  })
}

/**
 * 单站告警统计 - 查询
 * @param {Object} params - 请求参数
 * @param {string} [params.alarmGrade] - 告警等级
 * @param {string} [params.alarmName] - 告警名称
 * @param {string} [params.city] - 市(无值为地市维度，有值为区县维度)
 * @param {string} [params.county] - 区县
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编号
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商(不传为全量)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteCode] - 站址编码
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Array>} 返回单站告警统计列表，每个对象包含：
 * - city: 市
 * - count: 告警数
 * - county: 区县
 * - rank: 排名
 * - siteCode: 站址编码
 * - siteName: 站址名称
 */
export function getStationAlarmList(params) {
  return request({
    url: '/oa/station_alarm/list',
    method: 'get',
    params
  })
}

/**
 * 单站告警清单 - 分页查询
 * @param {Object} params - 请求参数
 * @param {string} [params.alarmGrade] - 告警等级
 * @param {string} [params.alarmName] - 告警名称
 * @param {string} [params.city] - 市(无值为地市维度，有值为区县维度)
 * @param {string} [params.county] - 区县
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编号
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商(不传为全量)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteCode] - 站址编码
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Object>} 返回分页数据对象，包含：
 * - list: 活动告警表数据列表
 * - total: 总数
 */
export function getStationAlarmPage(params) {
  return request({
    url: '/oa/station_alarm/page',
    method: 'get',
    params
  })
}

// ==================== 大屏应急保障信息表 接口 ====================

/**
 * 简报信息- 导出
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 地市
 * @param {string} [params.county] - 区县
 * @param {string} [params.dateType] - 数据类型（故障总数、超3小时、超6小时、超8小时）
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编码
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商（铁塔、电信、联通、移动）
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise} 返回导出结果
 */
export function exportGuaranteeInfo(params) {
  return request({
    url: '/oa/OA_GUARANTEE_INFO/export',
    method: 'get',
    params
  })
}

/**
 * 大屏应急保障信息表 - 下载模板
 * @returns {Promise} 返回模板文件
 */
export function exportGuaranteeInfoTemplate() {
  return request({
    url: '/oa/OA_GUARANTEE_INFO/exportTemplate',
    method: 'get'
  })
}

/**
 * 简报信息 - 导入
 * @param {FormData} formData - 包含文件的表单数据
 * @returns {Promise<Object>} 返回导入结果，包含：
 * - errorFileUrl: 异常文件url
 * - errorNum: 失败数量
 * - msg: 消息
 * - successNum: 成功数量
 * - taskName: 模块名称
 * - totalNum: 导入总数
 */
export function importGuaranteeInfo(formData) {
  return request({
    url: '/oa/OA_GUARANTEE_INFO/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 大屏应急保障信息表 - 查询
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 地市
 * @param {string} [params.county] - 区县
 * @param {string} [params.dateType] - 数据类型（故障总数、超3小时、超6小时、超8小时）
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编码
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商（铁塔、电信、联通、移动）
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Array>} 返回应急保障信息列表，每个对象包含：
 * - car: 应急车辆
 * - city: 地市
 * - county: 区县
 * - offline: 离线
 * - oilEngine: 发电油机
 * - other: 其他
 * - powerFailure: 停电
 * - satellitePhone: 卫星电话
 * - smoke: 烟雾/火灾
 * - staff: 保障人员
 * - suspectedWithdraw: 疑似退服
 * - temperature: 温度过高
 * - uav: 无人机
 * - undervoltage: 欠压
 * - water: 水浸
 * - withdraw: 退服
 */
export function getGuaranteeInfoPage(params) {
  return request({
    url: '/oa/OA_GUARANTEE_INFO/page',
    method: 'get',
    params
  })
}



// ==================== 大屏简报信息配置表 接口 ====================

/**
 * 大屏简报信息配置表 - 新增
 * @param {Object} data - 简报信息配置数据
 * @param {string} [data.alarmContent] - 告警内容展示
 * @param {string} [data.briefingType] - 简报类型(省报、地市报)
 * @param {string} [data.createBy] - 创建人
 * @param {string} [data.createTime] - 创建时间
 * @param {string} [data.displayEndTime] - 简报展示结束时间
 * @param {string} [data.displayStartTime] - 简报展示开始时间
 * @param {string} [data.name] - 简报命名
 * @param {string} [data.reason] - 应急原因
 * @param {string} [data.statisticsEndTime] - 告警统计结束时间
 * @param {string} [data.statisticsStartTime] - 告警统计开始时间
 * @param {string} [data.updateBy] - 修改人
 * @param {string} [data.updateTime] - 修改时间
 * @returns {Promise<string>} 返回操作结果
 */
export function addBriefingInfo(data) {
  return request({
    url: '/oa/OA_BRIEFING_INFO/add',
    method: 'post',
    data
  })
}

/**
 * 大屏简报信息配置表 - 删除
 * @param {Array<string>} idList - 要删除的ID列表
 * @returns {Promise<string>} 返回操作结果
 */
export function deleteBriefingInfo(idList) {
  return request({
    url: '/oa/OA_BRIEFING_INFO/delete',
    method: 'delete',
    data: idList
  })
}

/**
 * 大屏简报信息配置表 - 分页查询
 * @param {Object} params - 请求参数
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @returns {Promise<Object>} 返回分页数据对象，包含：
 * - list: 简报信息配置列表
 * - total: 总数
 */
export function getBriefingInfoPage(params) {
  return request({
    url: '/oa/OA_BRIEFING_INFO/page',
    method: 'get',
    params
  })
}

/**
 * 大屏简报信息配置表 - 修改
 * @param {Object} data - 简报信息配置数据
 * @param {string} [data.alarmContent] - 告警内容展示
 * @param {string} [data.briefingType] - 简报类型(省报、地市报)
 * @param {string} [data.createBy] - 创建人
 * @param {string} [data.createTime] - 创建时间
 * @param {string} [data.displayEndTime] - 简报展示结束时间
 * @param {string} [data.displayStartTime] - 简报展示开始时间
 * @param {string} data.id - ID
 * @param {string} [data.name] - 简报命名
 * @param {string} [data.reason] - 应急原因
 * @param {string} [data.statisticsEndTime] - 告警统计结束时间
 * @param {string} [data.statisticsStartTime] - 告警统计开始时间
 * @param {string} [data.updateBy] - 修改人
 * @param {string} [data.updateTime] - 修改时间
 * @returns {Promise<string>} 返回操作结果
 */
export function updateBriefingInfo(data) {
  return request({
    url: '/oa/OA_BRIEFING_INFO/update',
    method: 'put',
    data
  })
}

// ==================== 故障工单超时预警 接口 ====================

/**
 * 故障超时预警-PC - 导出
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 地市
 * @param {string} [params.county] - 区县
 * @param {string} [params.dateType] - 数据类型（故障总数、超3小时、超6小时、超8小时）
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编码
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商（铁塔、电信、联通、移动）
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise} 返回导出结果
 */
export function exportFaultTimeoutStatistics(params) {
  return request({
    url: '/oa/fault_timeout_statistics/export',
    method: 'get',
    params,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
  })
}

/**
 * 故障超时预警统计 - 查询
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 地市
 * @param {string} [params.county] - 区县
 * @param {string} [params.dateType] - 数据类型（故障总数、超3小时、超6小时、超8小时）
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编码
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商（铁塔、电信、联通、移动）
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Array>} 返回故障超时预警统计列表，每个对象包含：
 * - city: 地市
 * - county: 区县
 * - over3Hour: 超3小时
 * - over6Hour: 超6小时
 * - over8Hour: 超8小时
 * - total: 故障总数
 */
export function getFaultTimeoutStatisticsPage(params) {
  return request({
    url: '/oa/fault_timeout_statistics/page',
    method: 'get',
    params
  })
}

/**
 * 故障超时预警统计-明细
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 地市
 * @param {string} [params.county] - 区县
 * @param {string} [params.dateType] - 数据类型（故障总数、超3小时、超6小时、超8小时）
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编码
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商（铁塔、电信、联通、移动）
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Object>} 返回分页数据对象，包含：
 * - list: 故障工单表数据列表
 * - total: 总数
 */
export function getFaultTimeoutStatisticsDetail(params) {
  return request({
    url: '/oa/fault_timeout_statistics/record/detail/open',
    method: 'get',
    params
  })
}

// ==================== 气象预警表 接口 ====================

/**
 * 气象预警表 - 查询
 * @param {Object} params - 请求参数
 * @param {string} [params.city] - 市
 * @param {string} [params.happenTime] - 发生时间
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.updateTime] - 创建时间
 * @param {string} [params.warningType] - 预警类型
 * @returns {Promise<Array>} 返回气象预警列表，每个对象包含：
 * - city: 市
 * - county: 区县
 * - happenTime: 发生时间
 * - id: ID
 * - province: 省
 * - updateTime: 创建时间
 * - warningContent: 预警内容
 * - warningPicture: 预警图片
 * - warningType: 预警类型
 * - weight: 权重：红、橙、黄、蓝
 */
export function getWeatherWarningList(params) {
  return request({
    url: '/oa/py_warning/list',
    method: 'get',
    params
  })
}

// ==================== 注册验证 接口 ====================

/**
 * 注册
 * @param {Object} data - 用户注册数据
 * @param {string} [data.code] - 验证码
 * @param {string} [data.password] - 用户密码
 * @param {string} [data.username] - 用户名
 * @param {string} [data.uuid] - 唯一标识
 * @returns {Promise} 返回注册结果
 */
export function register(data) {
  return request({
    url: '/register',
    method: 'post',
    data
  })
}

// ==================== 活动告警表 接口 ====================

/**
 * 活动告警表 - 新增
 * @param {Object} data - 活动告警数据
 * @param {string} [data.alarmDetails] - 告警详情
 * @param {string} [data.alarmFrom] - 告警来源
 * @param {string} [data.alarmGrade] - 告警等级
 * @param {number} [data.alarmLast] - 告警历时(分钟)
 * @param {string} [data.alarmName] - 告警名称
 * @param {string} [data.alarmTime] - 告警发生时间
 * @param {string} [data.alarmWaterId] - 告警流水号ID
 * @param {string} [data.city] - 市
 * @param {string} [data.county] - 区县
 * @param {string} [data.deviceName] - 子设备名称
 * @param {string} [data.faultCode] - 故障单编号
 * @param {string} [data.id] - ID
 * @param {string} [data.isTimeout] - 是否超时
 * @param {string} [data.operationId] - 运维Id
 * @param {string} [data.operator] - 所属运营商
 * @param {string} [data.region] - 区域(铁塔自划)
 * @param {string} [data.signalId] - 信号量ID
 * @param {string} [data.siteCode] - 站址编码
 * @param {string} [data.siteGrade] - 站址保障等级
 * @param {string} [data.siteName] - 站址名称
 * @param {string} [data.siteRemarks] - 站址名备注
 * @param {string} [data.siteStatus] - 站址状态
 * @param {string} [data.siteType] - 站型(站址类型)
 * @param {string} [data.updateTime] - 数据更新时间
 * @returns {Promise<string>} 返回操作结果
 */
export function addActiveAlarm(data) {
  return request({
    url: '/oa/PY_ALARM',
    method: 'post',
    data
  })
}

/**
 * 活动告警表 - 修改
 * @param {Object} data - 活动告警数据
 * @param {string} [data.alarmDetails] - 告警详情
 * @param {string} [data.alarmFrom] - 告警来源
 * @param {string} [data.alarmGrade] - 告警等级
 * @param {number} [data.alarmLast] - 告警历时(分钟)
 * @param {string} [data.alarmName] - 告警名称
 * @param {string} [data.alarmTime] - 告警发生时间
 * @param {string} [data.alarmWaterId] - 告警流水号ID
 * @param {string} [data.city] - 市
 * @param {string} [data.county] - 区县
 * @param {string} [data.deviceName] - 子设备名称
 * @param {string} [data.faultCode] - 故障单编号
 * @param {string} [data.id] - ID
 * @param {string} [data.isTimeout] - 是否超时
 * @param {string} [data.operationId] - 运维Id
 * @param {string} [data.operator] - 所属运营商
 * @param {string} [data.region] - 区域(铁塔自划)
 * @param {string} [data.signalId] - 信号量ID
 * @param {string} [data.siteCode] - 站址编码
 * @param {string} [data.siteGrade] - 站址保障等级
 * @param {string} [data.siteName] - 站址名称
 * @param {string} [data.siteRemarks] - 站址名备注
 * @param {string} [data.siteStatus] - 站址状态
 * @param {string} [data.siteType] - 站型(站址类型)
 * @param {string} [data.updateTime] - 数据更新时间
 * @returns {Promise<string>} 返回操作结果
 */
export function updateActiveAlarm(data) {
  return request({
    url: '/oa/PY_ALARM',
    method: 'put',
    data
  })
}

/**
 * 活动告警表 - 删除
 * @param {Array<string>} idList - 要删除的ID列表
 * @returns {Promise<string>} 返回操作结果
 */
export function deleteActiveAlarm(idList) {
  return request({
    url: '/oa/PY_ALARM',
    method: 'delete',
    data: idList
  })
}

/**
 * 活动告警表 - 分页查询
 * @param {Object} params - 请求参数
 * @param {string} [params.alarmGrade] - 告警等级
 * @param {string} [params.alarmName] - 告警名称
 * @param {string} [params.city] - 市(无值为地市维度，有值为区县维度)
 * @param {string} [params.county] - 区县
 * @param {string} [params.endTime] - 结束时间
 * @param {string} [params.faultCode] - 故障单编号
 * @param {string} [params.isAsc] - 排序的方式 (desc 或者 asc)
 * @param {string} [params.operator] - 所属运营商(不传为全量)
 * @param {string} [params.orderByColumn] - 排序字段
 * @param {number} [params.pageNum] - 页码
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.siteCode] - 站址编码
 * @param {string} [params.siteName] - 站址名称
 * @param {string} [params.startTime] - 开始时间
 * @returns {Promise<Object>} 返回分页数据对象，包含：
 * - list: 活动告警表数据列表
 * - total: 总数
 */
export function getActiveAlarmPage(params) {
  return request({
    url: '/oa/PY_ALARM/page',
    method: 'get',
    params
  })
}

// ==================== 验证码 接口 ====================

/**
 * 生成验证码
 * @returns {Promise} 返回验证码图片
 */
export function getCaptchaImage() {
  return request({
    url: '/captchaImage',
    method: 'get'
  })
}

